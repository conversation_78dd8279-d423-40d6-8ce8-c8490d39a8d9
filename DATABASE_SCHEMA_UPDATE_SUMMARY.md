# 数据库字段更新总结

## 数据库修改

根据你提供的 SQL 语句，对以下两个表进行了字段添加：

### material_smart_description 表
- 添加 `namespace` varchar(100) NOT NULL - 命名空间，物料标识的另一种形式
- 添加 `schema_url` varchar(512) DEFAULT NULL - 物料schema详情URL  
- 添加 `publish_status` tinyint(4) NOT NULL DEFAULT '0' - 发布状态：0=草稿，1=正式发布
- 添加索引 `idx_namespace` 和 `idx_publish_status`

### material_smart_description_job 表
- 添加 `namespace` varchar(100) NOT NULL - 命名空间，物料标识的另一种形式
- 添加 `schema_url` varchar(512) DEFAULT NULL - 物料schema详情URL
- 添加索引 `idx_namespace`

## 代码修改

### 1. 数据库模型更新

#### MaterialSmartDescription 模型 (`packages/server/src/database/models/material-smart-description.model.ts`)
- 更新 `MaterialSmartDescriptionAttributes` 接口，添加新字段
- 更新模型类属性定义
- 在 `initModel` 方法中添加新字段的数据库定义
- 添加相应的索引配置

#### MaterialSmartDescriptionJob 模型 (`packages/server/src/database/models/material-smart-description-job.model.ts`)
- 更新 `MaterialSmartDescriptionJobAttributes` 接口，添加新字段
- 更新模型类属性定义
- 在 `initModel` 方法中添加新字段的数据库定义
- 添加相应的索引配置

### 2. 类型定义更新

#### material-smart-description.d.ts
- 添加 `PublishStatus` 枚举定义（DRAFT=0, PUBLISHED=1）
- 更新 `EnrichedDescription` 接口，添加新字段
- 更新 `RawDescriptionQueryResult` 接口，添加新字段

### 3. 服务层更新

#### MaterialSmartDescriptionService
- 更新 `createSmartDescription` 方法，支持新字段参数
- 更新 `createSmartDescriptionFromExisting` 方法，保持新字段值
- 更新 `findLatestDescriptionByMaterialIdentifier` 方法，支持通过 namespace 查询
- 添加新方法：
  - `findDescriptionsByNamespace` - 根据 namespace 模糊查询
  - `countDescriptionsByNamespace` - 统计 namespace 匹配的描述总数
  - `findDescriptionsByPublishStatus` - 根据发布状态查询
  - `countDescriptionsByPublishStatus` - 统计发布状态匹配的描述总数
  - `updatePublishStatus` - 更新发布状态

#### MaterialSmartDescriptionJobService
- 更新 `createEmptyJob` 方法，支持新字段参数
- 更新 `trigger` 方法中的调用，传递新字段值
- 添加新方法：
  - `findJobsByNamespace` - 根据 namespace 模糊查询作业
  - `countJobsByNamespace` - 统计 namespace 匹配的作业总数

### 4. 控制器层更新

#### MaterialSmartDescriptionController
- 更新查询结果的类型定义，包含新字段
- 更新 `EnrichedDescription` 构建逻辑，包含新字段
- 添加新的 API 接口：
  - `GET /namespace/:namespace` - 根据 namespace 模糊查询描述列表
  - `GET /publish-status/:status` - 根据发布状态获取描述列表
  - `PUT /:id/publish-status` - 更新发布状态

#### MaterialSmartDescriptionJobController
- 添加新的 API 接口：
  - `GET /namespace/:namespace` - 根据 namespace 模糊查询作业列表

## Schema URL 业务逻辑

### 数据流向
1. **Job 表的 schemaUrl**：
   - 来源：`materialDetail.currentVersion.schema` 上传到 CDN 后的地址
   - 处理：在 `trigger` 方法中，将物料的 schema 对象上传到 CDN，获得 CDN 地址
   - 存储：将 CDN 地址存储到 `material_smart_description_job.schema_url` 字段

2. **Description 表的 schemaUrl**：
   - 来源：继承自对应的 Job 表
   - 处理：在创建 Description 记录时，从对应的 Job 记录中获取 `schemaUrl`
   - 存储：将继承的 URL 存储到 `material_smart_description.schema_url` 字段

### 实现细节
- CDN 上传目录：`material-schemas`
- 文件命名格式：`material-schema-{materialId}-{version}.json`
- 错误处理：如果 CDN 上传失败，会记录警告日志，但不影响主流程
- 继承机制：Description 表总是从对应的 Job 表获取最新的 `schemaUrl`

## 新增功能

1. **命名空间支持**：现在可以通过 namespace 模糊查询和管理物料描述和作业
2. **Schema URL 支持**：
   - Job 表：存储物料 schema 上传到 CDN 后的地址
   - Description 表：继承自对应 Job 表的 schema URL
3. **发布状态管理**：支持草稿和正式发布两种状态，可以进行状态管理
4. **增强的查询能力**：支持多种查询方式（namespace 模糊查询、发布状态等）

## 模糊查询功能

### 实现方式
- 使用 Sequelize 的 `Op.like` 操作符实现模糊查询
- 查询模式：`%${namespace}%`，支持前后匹配
- 适用于 namespace 字段的部分匹配查询

### 支持的接口
- `GET /material-smart-description/namespace/:namespace` - 描述列表模糊查询
- `GET /material-smart-description-job/namespace/:namespace` - 作业列表模糊查询

### 使用示例
- 查询 namespace 包含 "react" 的所有记录：`/namespace/react`
- 查询 namespace 包含 "component" 的所有记录：`/namespace/component`
- 支持中文和英文的模糊匹配

## 分页优化

### 问题解决
- **修复总数计算错误**：之前 `total = jobs.length` 只是当前页记录数，现在使用专门的 `count` 方法获取真实总数
- **优化性能**：使用 `Promise.all` 并行执行数据查询和计数查询，提高响应速度
- **准确的分页信息**：基于真实总数计算 `totalPage`、`hasNext`、`hasPrev` 等分页信息

### 实现方式
- 添加专门的计数方法：`countJobsByNamespace`、`countDescriptionsByNamespace`、`countDescriptionsByPublishStatus`
- Controller 层使用并行查询：`Promise.all([查询数据, 统计总数])`
- 保持查询条件一致性：计数查询与数据查询使用相同的 where 条件

### 分页响应格式
```typescript
{
  list: T[],           // 当前页数据
  total: number,       // 符合条件的总记录数
  pageNum: number,     // 当前页码
  pageSize: number,    // 每页大小
  totalPage: number,   // 总页数
  hasNext: boolean,    // 是否有下一页
  hasPrev: boolean     // 是否有上一页
}
```

## 向后兼容性

- 所有现有的 API 接口保持不变
- 新字段都有合理的默认值
- 现有的查询逻辑继续工作
- 新功能通过新的 API 接口提供

## 注意事项

1. 新字段 `namespace` 是必填字段，在创建记录时需要提供
2. `schema_url` 是可选字段，可以为 null
3. `publish_status` 默认为 0（草稿状态）
4. 建议在生产环境部署前先在测试环境验证所有功能
